# 4. 核心实现：统一AOP与分片路由引擎

## 4.1 统一AOP切面设计

### 4.1.1 切面架构与职责划分

为了实现分库分表的统一管理，我们设计了一个职责清晰、可扩展的AOP架构。核心设计原则：

+ **职责分离**：切面只负责拦截和调度，具体的路由逻辑由专门的引擎处理
+ **上下文传递**：通过统一的上下文对象传递路由所需的所有信息
+ **策略模式**：根据不同的操作类型和返回值类型选择相应的处理策略

**核心接口定义：**

```java
// 统一切面接口
interface ShardingAspect {
    Object handleSharding(ProceedingJoinPoint point);
    QueryContext buildContext(ProceedingJoinPoint point);
}

// 执行引擎接口
interface ShardingExecutionEngine {
    Object execute(QueryContext context, ProceedingJoinPoint point);
}

// 查询上下文
class QueryContext {
    String methodName;           // 方法名
    Object[] args;              // 方法参数
    ReturnType returnType;      // 返回值类型
    OperationType operationType; // 操作类型
    String guaranteeNo;         // 融担号（分库键）
    String shardingKey;         // 分表键
    boolean hasShardingKey;     // 是否包含分表键
}

enum ReturnType { VOID, SINGLE_OBJECT, LIST, PAGE, AGGREGATION }
enum OperationType { INSERT, UPDATE, DELETE, SELECT }
```

**切面处理流程：**

```
1. 拦截DAO层方法调用
2. 从ServiceContext获取融担号
3. 分析方法签名确定操作类型和返回值类型
4. 构建QueryContext上下文对象
5. 委托给ShardingExecutionEngine执行
6. 返回执行结果
```
```

### 4.1.2 上下文信息提取与传递

查询上下文是整个分片路由的核心数据结构，负责承载路由决策所需的所有信息。设计要点：

+ **信息完整性**：包含方法签名、参数、返回值类型等完整信息
+ **路由标识**：明确标识分库键、分表键的存在情况
+ **查询特征**：识别查询的复杂度和类型，用于策略选择

**上下文构建逻辑：**

```
buildQueryContext(ProceedingJoinPoint point) {
    // 1. 提取基础信息
    methodName = point.getMethod().getName()
    args = point.getArgs()

    // 2. 分析返回值类型
    returnType = analyzeReturnType(point.getMethod().getReturnType())

    // 3. 确定操作类型
    operationType = determineOperationType(methodName)

    // 4. 获取路由信息
    guaranteeNo = ServiceContext.getGuaranteeNo()
    shardingKey = extractShardingKey(args, methodName)

    // 5. 分析查询特征
    queryFeatures = analyzeQueryFeatures(methodName, args)

    return new QueryContext(...)
}
```

**返回值类型判断规则：**

| 返回值类型 | 判断条件 | 处理策略 |
|-----------|----------|----------|
| VOID | void.class | 写操作 |
| SINGLE_OBJECT | 普通对象类型 | 单点查询 |
| LIST | Collection子类 | 列表查询 |
| PAGE | 包含"Page"的类型 | 分页查询 |
| AGGREGATION | 基础类型/包装类型 | 聚合查询 |
```

### 4.1.3 分库分表路由的统一入口

执行引擎是整个分片系统的核心调度器，负责根据查询上下文选择合适的处理策略。设计原则：

+ **策略分离**：读写操作采用不同的处理策略
+ **事务管理**：确保写操作的事务一致性
+ **监控统计**：记录各种操作的执行情况

**执行引擎架构：**

```
ShardingExecutionEngine {
    execute(QueryContext context, ProceedingJoinPoint point) {
        // 1. 根据操作类型选择处理器
        if (context.isWriteOperation()) {
            return writeHandler.handle(context, point)
        } else {
            return readHandler.handle(context, point)
        }
    }
}
```

**处理器接口定义：**

```java
// 写操作处理器接口
interface WriteOperationHandler {
    Object handle(QueryContext context, ProceedingJoinPoint point);
}

// 读操作处理器接口
interface ReadOperationHandler {
    Object handle(QueryContext context, ProceedingJoinPoint point);
}

// 查询策略管理器接口
interface QueryStrategyManager {
    QueryStrategy determineStrategy(QueryContext context);
}

enum QueryStrategy {
    DIRECT_SHARDING,    // 直接分片查询
    INDEX_THEN_SCAN,    // 索引表辅助查询
    LIMITED_SCAN,       // 受限全表扫描
    SCROLL_SCAN,        // 滚动分页扫描
    AGGREGATION_SCAN,   // 聚合查询扫描
    FORBIDDEN           // 禁止查询
}
```
```

## 4.2 HintManager强制路由实现

### 4.2.1 基于ServiceContext的路由策略

HintManager是ShardingSphere提供的强制路由机制，允许在运行时动态指定路由目标。我们基于ServiceContext中的上下文信息来实现精确路由。

**核心设计思路：**

+ **上下文驱动**：从ServiceContext获取融担号，无需SQL中包含分库条件
+ **分层路由**：分库路由基于融担号，分表路由基于业务分片键
+ **资源管理**：HintManager采用try-with-resources模式，确保资源正确释放

**路由管理器接口：**

```java
interface HintRoutingManager {
    // 应用分库路由
    void applyDatabaseHint(String guaranteeNo);

    // 应用分表路由
    void applyTableHint(String tableName, String shardingKey);

    // 同时应用分库分表路由
    void applyBothHints(String guaranteeNo, String tableName, String shardingKey);
}
```

**路由应用流程：**

```
applyDatabaseHint(guaranteeNo) {
    validate(guaranteeNo)

    try (HintManager hint = HintManager.getInstance()) {
        hint.setDatabaseShardingValue("t_order", "guarantee_no", guaranteeNo)
    }
}

applyTableHint(tableName, shardingKey) {
    if (shardingKey != null) {
        try (HintManager hint = HintManager.getInstance()) {
            hint.setTableShardingValue(tableName, "sharding_key", shardingKey)
        }
    }
}
```
```

### 4.2.2 分库路由：融担号的处理

分库路由是系统的基础路由机制，所有操作都必须先确定目标数据库。设计要点：

+ **融担号获取**：优先从QueryContext获取，兜底从ServiceContext获取
+ **路由必需性**：融担号是分库的必需条件，缺失时抛出异常
+ **上下文更新**：路由成功后更新QueryContext中的融担号信息

**分库路由处理器：**

```java
interface DatabaseRoutingHandler {
    void routeToDatabase(QueryContext context);
}
```

**分库路由逻辑：**

```
routeToDatabase(QueryContext context) {
    // 1. 获取融担号
    guaranteeNo = context.getGuaranteeNo()
    if (guaranteeNo == null) {
        guaranteeNo = ServiceContext.getGuaranteeNo()
    }

    // 2. 验证融担号
    if (guaranteeNo == null) {
        throw ShardingException("融担号缺失，无法进行分库路由")
    }

    // 3. 应用分库路由
    hintManager.applyDatabaseHint(guaranteeNo)

    // 4. 更新上下文
    context.setGuaranteeNo(guaranteeNo)
}
```
```

### 4.2.3 分表路由：分片键的动态获取

分表路由需要根据具体的业务场景动态获取分片键，这是实现精确路由的关键环节。

**分片键提取策略：**

+ **参数提取**：从方法参数中提取分片键（如实体对象的ID字段）
+ **SQL解析**：从SQL语句中解析分片键（如WHERE id = 'xxx'）
+ **兜底机制**：无分片键时标记为广播查询

**分表路由处理器：**

```java
interface TableRoutingHandler {
    void routeToTable(QueryContext context, String tableName);
}

interface ShardingKeyExtractor {
    String extractFromArgs(QueryContext context);
    String extractFromSql(String sql);
}
```

**分表路由逻辑：**

```
routeToTable(QueryContext context, String tableName) {
    // 1. 尝试从方法参数提取分片键
    shardingKey = keyExtractor.extractFromArgs(context)

    // 2. 兜底：从SQL中解析分片键
    if (shardingKey == null) {
        shardingKey = keyExtractor.extractFromSql(context.getSql())
    }

    // 3. 应用分表路由
    if (shardingKey != null) {
        hintManager.applyTableHint(tableName, shardingKey)
        context.setHasShardingKey(true)
    } else {
        context.setHasShardingKey(false)  // 标记为广播查询
    }
}
```

**分片键提取规则：**

| 操作类型 | 提取策略 | 示例 |
|---------|----------|------|
| INSERT/UPDATE | 从实体对象的ID字段提取 | entity.getId() |
| SELECT | 从方法参数中查找String类型的ID | findById(String id) |
| SQL解析 | 正则匹配WHERE id = 'xxx' | WHERE id = 'ORDER_123' |
```

## 4.3 事务一致性保障机制

### 4.3.1 主表与索引表的原子性写入

为了确保主表和索引表的数据一致性，我们设计了统一的写操作处理机制。核心原则：

+ **事务边界统一**：主表和索引表的操作在同一个事务中完成
+ **异常回滚**：任何一个操作失败都会触发整个事务回滚
+ **操作顺序**：先执行主表操作，成功后再更新索引表

**写操作处理器接口：**

```java
interface WriteOperationHandler {
    Object handle(QueryContext context, ProceedingJoinPoint point);
}

interface IndexTableManager {
    void insertIndexRecord(QueryContext context, Object result);
    void updateIndexRecord(QueryContext context, Object result);
}
```

**写操作处理流程：**

```
@Transactional
handle(QueryContext context, ProceedingJoinPoint point) {
    // 1. 应用路由策略
    dbRoutingHandler.routeToDatabase(context)
    tableRoutingHandler.routeToTable(context, tableName)

    // 2. 执行主表操作
    result = point.proceed()

    // 3. 同步更新索引表
    if (needsIndexTableUpdate(context)) {
        try {
            indexTableManager.updateIndexRecord(context, result)
        } catch (Exception e) {
            // 抛出异常，触发事务回滚
            throw new IndexTableUpdateException(e)
        }
    }

    return result
}
```

**索引表更新策略：**

| 操作类型 | 索引表处理 | 说明 |
|---------|-----------|------|
| INSERT | 插入索引记录 | 提取关键字段插入索引表 |
| UPDATE | 更新索引记录 | 先尝试更新，失败则插入 |
| DELETE | 暂不处理 | 依赖数据生命周期管理 |
```

### 4.3.2 索引表管理器

索引表管理器负责维护索引表的数据同步：

```java
@Component
public class IndexTableManager {

    private static final Logger log = LoggerFactory.getLogger(IndexTableManager.class);

    @Autowired
    private IndexTableMapper indexTableMapper;

    @Value("${index.table.enabled:true}")
    private boolean indexTableEnabled;

    public void insertIndexRecord(QueryContext context, Object result) {
        if (!indexTableEnabled) {
            return;
        }

        IndexRecord record = buildIndexRecord(context, result);
        if (record != null) {
            indexTableMapper.insert(record);
            log.debug("Index record inserted: {}", record);
        }
    }

    public void updateIndexRecord(QueryContext context, Object result) {
        if (!indexTableEnabled) {
            return;
        }

        IndexRecord record = buildIndexRecord(context, result);
        if (record != null) {
            // 先尝试更新，如果不存在则插入
            int updated = indexTableMapper.updateById(record);
            if (updated == 0) {
                indexTableMapper.insert(record);
                log.debug("Index record inserted (update failed): {}", record);
            } else {
                log.debug("Index record updated: {}", record);
            }
        }
    }

    private IndexRecord buildIndexRecord(QueryContext context, Object result) {
        try {
            // 从结果或参数中提取索引信息
            Object entity = extractEntity(context, result);
            if (entity == null) {
                return null;
            }

            return IndexRecord.builder()
                    .id(getFieldValue(entity, "id"))
                    .userId(getFieldValue(entity, "userId"))
                    .orderNo(getFieldValue(entity, "orderNo"))
                    .phone(getFieldValue(entity, "phone"))
                    .createTime(getFieldValue(entity, "createTime"))
                    .build();

        } catch (Exception e) {
            log.warn("Failed to build index record: {}", e.getMessage());
            return null;
        }
    }

    private Object extractEntity(QueryContext context, Object result) {
        // 从方法参数中获取实体对象
        Object[] args = context.getArgs();
        if (args != null && args.length > 0) {
            return args[0]; // 通常第一个参数是实体对象
        }
        return null;
    }

    private String getFieldValue(Object entity, String fieldName) {
        try {
            Field field = entity.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(entity);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }
}

@Data
@Builder
public class IndexRecord {
    private String id;
    private String userId;
    private String orderNo;
    private String phone;
    private Date createTime;
}
```

### 4.3.3 事务传播级别的控制

为了确保事务的正确传播，我们需要精确控制事务边界：

```java
@Configuration
@EnableTransactionManagement
public class TransactionConfig {

    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public TransactionTemplate transactionTemplate(PlatformTransactionManager transactionManager) {
        TransactionTemplate template = new TransactionTemplate(transactionManager);
        template.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        template.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        template.setTimeout(30); // 30秒超时
        return template;
    }
}

@Component
public class TransactionHelper {

    @Autowired
    private TransactionTemplate transactionTemplate;

    public <T> T executeInTransaction(Supplier<T> operation) {
        return transactionTemplate.execute(status -> {
            try {
                return operation.get();
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new RuntimeException("Transaction execution failed", e);
            }
        });
    }

    public void executeInTransaction(Runnable operation) {
        transactionTemplate.execute(status -> {
            try {
                operation.run();
                return null;
            } catch (Exception e) {
                status.setRollbackOnly();
                throw new RuntimeException("Transaction execution failed", e);
            }
        });
    }
}
```

## 4.4 分片算法的具体实现

### 4.4.1 自定义Hint分片算法

我们实现了基于Hint的分片算法，支持从上下文获取路由信息：

```java
public class GuaranteeNoHintShardingAlgorithm implements HintShardingAlgorithm<String> {

    private static final Logger log = LoggerFactory.getLogger(GuaranteeNoHintShardingAlgorithm.class);

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       HintShardingValue<String> shardingValue) {

        String guaranteeNo = extractGuaranteeNo(shardingValue);

        if (StringUtils.isBlank(guaranteeNo)) {
            throw new ShardingException("Guarantee number is required for database sharding");
        }

        // 计算目标数据库
        int dbIndex = calculateDatabaseIndex(guaranteeNo, availableTargetNames.size());

        String targetDb = availableTargetNames.stream()
                .filter(name -> name.endsWith("_" + dbIndex))
                .findFirst()
                .orElseThrow(() -> new ShardingException("No target database found for index: " + dbIndex));

        log.debug("Database sharding: guarantee_no={}, target={}", guaranteeNo, targetDb);

        return Collections.singletonList(targetDb);
    }

    private String extractGuaranteeNo(HintShardingValue<String> shardingValue) {
        // 优先从Hint值获取
        if (!shardingValue.getValues().isEmpty()) {
            return shardingValue.getValues().iterator().next();
        }

        // 从上下文获取（兜底方案）
        return ServiceContext.getGuaranteeNo();
    }

    private int calculateDatabaseIndex(String guaranteeNo, int dbCount) {
        return Math.abs(guaranteeNo.hashCode()) % dbCount;
    }
}
```

### 4.4.2 分表算法：业务分片键处理

分表算法支持多种分片键的处理：

```java
public class BusinessKeyHintShardingAlgorithm implements HintShardingAlgorithm<String> {

    private static final Logger log = LoggerFactory.getLogger(BusinessKeyHintShardingAlgorithm.class);

    private static final int TABLE_COUNT = 128;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                       HintShardingValue<String> shardingValue) {

        String shardingKey = extractShardingKey(shardingValue);

        if (StringUtils.isBlank(shardingKey)) {
            // 如果没有分表键，返回所有表（广播查询）
            log.warn("No sharding key provided, broadcasting to all tables");
            return availableTargetNames;
        }

        // 计算目标表
        int tableIndex = calculateTableIndex(shardingKey);

        String targetTable = availableTargetNames.stream()
                .filter(name -> name.endsWith("_" + tableIndex))
                .findFirst()
                .orElseThrow(() -> new ShardingException("No target table found for index: " + tableIndex));

        log.debug("Table sharding: sharding_key={}, target={}", shardingKey, targetTable);

        return Collections.singletonList(targetTable);
    }

    private String extractShardingKey(HintShardingValue<String> shardingValue) {
        if (!shardingValue.getValues().isEmpty()) {
            return shardingValue.getValues().iterator().next();
        }
        return null;
    }

    private int calculateTableIndex(String shardingKey) {
        return Math.abs(shardingKey.hashCode()) % TABLE_COUNT;
    }
}

## 4.5 查询策略执行引擎

### 4.5.1 查询策略的判断与分发

读操作处理器负责根据查询上下文选择合适的查询策略：

```java
@Service
public class ReadOperationHandler {

    private static final Logger log = LoggerFactory.getLogger(ReadOperationHandler.class);

    @Autowired
    private DatabaseRoutingHandler dbRoutingHandler;

    @Autowired
    private TableRoutingHandler tableRoutingHandler;

    @Autowired
    private QueryStrategyManager strategyManager;

    @Autowired
    private IndexTableQueryExecutor indexQueryExecutor;

    @Autowired
    private DirectQueryExecutor directQueryExecutor;

    @Autowired
    private DegradedQueryExecutor degradedQueryExecutor;

    public Object handle(QueryContext context, ProceedingJoinPoint point) throws Throwable {
        // 1. 应用分库路由
        dbRoutingHandler.routeToDatabase(context);

        // 2. 尝试应用分表路由
        String tableName = extractTableName(context);
        tableRoutingHandler.routeToTable(context, tableName);

        // 3. 确定查询策略
        QueryStrategy strategy = strategyManager.determineStrategy(context);

        // 4. 根据策略执行查询
        return executeQuery(strategy, context, point);
    }

    private Object executeQuery(QueryStrategy strategy, QueryContext context,
                              ProceedingJoinPoint point) throws Throwable {

        switch (strategy) {
            case DIRECT_SHARDING:
                return directQueryExecutor.execute(context, point);

            case INDEX_THEN_SCAN:
                return indexQueryExecutor.executeWithFallback(context, point);

            case LIMITED_SCAN:
            case SCROLL_SCAN:
            case AGGREGATION_SCAN:
                return degradedQueryExecutor.execute(strategy, context, point);

            case FORBIDDEN:
                throw new QueryNotSupportedException(
                    "Query not supported: " + context.getMethodName());

            default:
                throw new IllegalStateException("Unknown query strategy: " + strategy);
        }
    }

    private String extractTableName(QueryContext context) {
        // 从方法名中提取表名
        String methodName = context.getMethodName();
        if (methodName.contains("Order")) {
            return "t_order";
        }
        return "t_order"; // 默认表名
    }
}
```

### 4.5.2 索引表查询的实现

索引表查询执行器实现了两阶段查询逻辑：

```java
@Component
public class IndexTableQueryExecutor {

    private static final Logger log = LoggerFactory.getLogger(IndexTableQueryExecutor.class);

    @Autowired
    private IndexTableMapper indexTableMapper;

    @Autowired
    private HintRoutingManager hintManager;

    @Autowired
    private DegradedQueryExecutor degradedQueryExecutor;

    public Object executeWithFallback(QueryContext context, ProceedingJoinPoint point) throws Throwable {
        try {
            // 第一阶段：查询索引表
            List<String> shardingKeys = queryIndexTable(context);

            if (shardingKeys.isEmpty()) {
                log.debug("Index table miss, falling back to degraded query: {}", context.getMethodName());
                return degradedQueryExecutor.execute(QueryStrategy.LIMITED_SCAN, context, point);
            }

            // 第二阶段：根据分片键精确查询
            return executeShardingQuery(shardingKeys, context, point);

        } catch (Exception e) {
            log.error("Index table query failed, falling back: method={}, error={}",
                     context.getMethodName(), e.getMessage());
            return degradedQueryExecutor.execute(QueryStrategy.LIMITED_SCAN, context, point);
        }
    }

    private List<String> queryIndexTable(QueryContext context) {
        // 构建索引表查询条件
        IndexQueryCondition condition = buildIndexQueryCondition(context);

        if (condition == null) {
            return Collections.emptyList();
        }

        // 查询索引表
        List<IndexRecord> records = indexTableMapper.selectByCondition(condition);

        // 提取分片键
        return records.stream()
                .map(IndexRecord::getId)
                .collect(Collectors.toList());
    }

    private IndexQueryCondition buildIndexQueryCondition(QueryContext context) {
        Object[] args = context.getArgs();
        if (args == null || args.length == 0) {
            return null;
        }

        // 根据方法名和参数构建查询条件
        String methodName = context.getMethodName();

        if (methodName.contains("ByUserId")) {
            return IndexQueryCondition.builder()
                    .userId((String) args[0])
                    .build();
        } else if (methodName.contains("ByOrderNo")) {
            return IndexQueryCondition.builder()
                    .orderNo((String) args[0])
                    .build();
        } else if (methodName.contains("ByPhone")) {
            return IndexQueryCondition.builder()
                    .phone((String) args[0])
                    .build();
        }

        return null;
    }

    private Object executeShardingQuery(List<String> shardingKeys, QueryContext context,
                                      ProceedingJoinPoint point) throws Throwable {

        if (context.getReturnType() == ReturnType.SINGLE_OBJECT && shardingKeys.size() == 1) {
            // 单点查询：直接使用分片键路由
            String shardingKey = shardingKeys.get(0);
            hintManager.applyTableHint("t_order", shardingKey);

            return point.proceed();

        } else if (context.getReturnType() == ReturnType.LIST) {
            // 列表查询：批量查询多个分片
            return executeBatchQuery(shardingKeys, context, point);
        }

        return point.proceed();
    }

    private Object executeBatchQuery(List<String> shardingKeys, QueryContext context,
                                   ProceedingJoinPoint point) throws Throwable {
        List<Object> results = new ArrayList<>();

        for (String shardingKey : shardingKeys) {
            try {
                // 为每个分片键单独执行查询
                hintManager.applyTableHint("t_order", shardingKey);
                Object result = point.proceed();

                if (result != null) {
                    if (result instanceof Collection) {
                        results.addAll((Collection<?>) result);
                    } else {
                        results.add(result);
                    }
                }
            } catch (Exception e) {
                log.warn("Batch query failed for sharding key: {}, error: {}",
                        shardingKey, e.getMessage());
            }
        }

        return results;
    }
}

@Data
@Builder
public class IndexQueryCondition {
    private String userId;
    private String orderNo;
    private String phone;
    private Date startTime;
    private Date endTime;
}
```

### 4.5.3 降级查询的安全控制

降级查询执行器实现了各种安全控制机制：

```java
@Component
public class DegradedQueryExecutor {

    private static final Logger log = LoggerFactory.getLogger(DegradedQueryExecutor.class);

    @Value("${query.degraded.max.tables:32}")
    private int maxScanTables;

    @Value("${query.degraded.timeout.seconds:10}")
    private int timeoutSeconds;

    @Value("${query.degraded.max.results:100}")
    private int maxResults;

    @Autowired
    private QueryMonitor queryMonitor;

    public Object execute(QueryStrategy strategy, QueryContext context,
                         ProceedingJoinPoint point) throws Throwable {

        // 记录降级查询
        queryMonitor.recordDegradedQuery(strategy, context);

        // 应用安全限制
        applySafetyLimits(strategy, context);

        try {
            // 设置查询超时
            return executeWithTimeout(point, timeoutSeconds);

        } catch (Exception e) {
            log.error("Degraded query execution failed: strategy={}, method={}, error={}",
                     strategy, context.getMethodName(), e.getMessage());
            throw e;
        }
    }

    private void applySafetyLimits(QueryStrategy strategy, QueryContext context) {
        switch (strategy) {
            case LIMITED_SCAN:
                // 列表查询强制LIMIT
                if (context.getForcedLimit() == 0) {
                    context.setForcedLimit(maxResults);
                }
                logWarning("Limited scan query executed", context);
                break;

            case SCROLL_SCAN:
                // 滚动分页检查
                if (context.getPageSize() > maxResults) {
                    throw new QueryException("分页大小超过限制: " + maxResults);
                }
                logWarning("Scroll pagination query executed", context);
                break;

            case AGGREGATION_SCAN:
                // 聚合查询相对安全，只记录日志
                log.info("Aggregation query executed: {}", context.getMethodName());
                break;
        }
    }

    private Object executeWithTimeout(ProceedingJoinPoint point, int timeoutSeconds) throws Throwable {
        ExecutorService executor = Executors.newSingleThreadExecutor();

        try {
            Future<Object> future = executor.submit(() -> {
                try {
                    return point.proceed();
                } catch (Throwable e) {
                    throw new RuntimeException(e);
                }
            });

            return future.get(timeoutSeconds, TimeUnit.SECONDS);

        } catch (TimeoutException e) {
            throw new QueryTimeoutException("Query timeout after " + timeoutSeconds + " seconds");
        } finally {
            executor.shutdown();
        }
    }

    private void logWarning(String message, QueryContext context) {
        log.warn("{}: method={}, returnType={}",
                message, context.getMethodName(), context.getReturnType());

        // 发送告警
        queryMonitor.sendAlert(message, context);
    }
}

@Component
public class DirectQueryExecutor {

    public Object execute(QueryContext context, ProceedingJoinPoint point) throws Throwable {
        // 直接执行查询（已经应用了路由）
        return point.proceed();
    }
}

// 自定义异常类
public class QueryNotSupportedException extends RuntimeException {
    public QueryNotSupportedException(String message) {
        super(message);
    }
}

public class QueryTimeoutException extends RuntimeException {
    public QueryTimeoutException(String message) {
        super(message);
    }
}

public class IndexTableUpdateException extends RuntimeException {
    public IndexTableUpdateException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

通过这套完整的核心实现，我们实现了：

1. **统一的AOP切面**：职责清晰的分层架构
2. **强制路由机制**：基于HintManager的精确路由
3. **事务一致性**：主表和索引表的原子性操作
4. **灵活的分片算法**：支持多种分片策略
5. **智能的查询策略**：根据场景自动选择最优执行方式
6. **完善的安全控制**：超时、限流、监控等保障机制

这套实现为分库分表系统提供了坚实的技术基础，确保了系统的高性能、高可用和可维护性。
```
