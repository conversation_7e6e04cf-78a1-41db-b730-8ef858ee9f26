# 4. 核心实现：统一AOP与分片路由引擎

## 4.1 统一AOP切面设计

### 4.1.1 切面架构与职责划分

为了实现分库分表的统一管理，我们设计了一个职责清晰、可扩展的AOP架构。核心设计原则：

+ **职责分离**：切面只负责拦截和调度，具体的路由逻辑由专门的引擎处理
+ **上下文传递**：通过统一的上下文对象传递路由所需的所有信息
+ **策略模式**：根据不同的操作类型和返回值类型选择相应的处理策略

**核心接口定义：**

```java
// 统一切面接口
interface ShardingAspect {
    Object handleSharding(ProceedingJoinPoint point);
    QueryContext buildContext(ProceedingJoinPoint point);
}

// 执行引擎接口
interface ShardingExecutionEngine {
    Object execute(QueryContext context, ProceedingJoinPoint point);
}

// 查询上下文
class QueryContext {
    String methodName;           // 方法名
    Object[] args;              // 方法参数
    ReturnType returnType;      // 返回值类型
    OperationType operationType; // 操作类型
    String guaranteeNo;         // 融担号（分库键）
    String shardingKey;         // 分表键
    boolean hasShardingKey;     // 是否包含分表键
}

enum ReturnType { VOID, SINGLE_OBJECT, LIST, PAGE, AGGREGATION }
enum OperationType { INSERT, UPDATE, DELETE, SELECT }
```

**切面处理流程：**

```
1. 拦截DAO层方法调用
2. 从ServiceContext获取融担号
3. 分析方法签名确定操作类型和返回值类型
4. 构建QueryContext上下文对象
5. 委托给ShardingExecutionEngine执行
6. 返回执行结果
```

### 4.1.2 执行引擎的策略分发

执行引擎是整个分片系统的核心调度器，负责根据查询上下文选择合适的处理策略：

```
ShardingExecutionEngine.execute(context, point) {
    if (context.isWriteOperation()) {
        return writeHandler.handle(context, point)
    } else {
        return readHandler.handle(context, point)
    }
}
```

**处理器接口定义：**

```java
interface WriteOperationHandler {
    Object handle(QueryContext context, ProceedingJoinPoint point);
}

interface ReadOperationHandler {
    Object handle(QueryContext context, ProceedingJoinPoint point);
}

enum QueryStrategy {
    DIRECT_SHARDING,    // 直接分片查询
    INDEX_THEN_SCAN,    // 索引表辅助查询
    LIMITED_SCAN,       // 受限全表扫描
    SCROLL_SCAN,        // 滚动分页扫描
    AGGREGATION_SCAN,   // 聚合查询扫描
    FORBIDDEN           // 禁止查询
}
```

## 4.2 HintManager强制路由实现

### 4.2.1 基于ServiceContext的路由策略

HintManager是ShardingSphere提供的强制路由机制，允许在运行时动态指定路由目标。核心设计：

+ **上下文驱动**：从ServiceContext获取融担号，无需SQL中包含分库条件
+ **分层路由**：分库路由基于融担号，分表路由基于业务分片键
+ **资源管理**：采用try-with-resources模式，确保资源正确释放

**路由管理器接口：**

```java
interface HintRoutingManager {
    void applyDatabaseHint(String guaranteeNo);
    void applyTableHint(String tableName, String shardingKey);
    void applyBothHints(String guaranteeNo, String tableName, String shardingKey);
}
```

### 4.2.2 分库分表路由逻辑

**分库路由流程：**

```
routeToDatabase(QueryContext context) {
    // 1. 获取融担号
    guaranteeNo = context.getGuaranteeNo() ?: ServiceContext.getGuaranteeNo()
    
    // 2. 验证融担号
    if (guaranteeNo == null) {
        throw ShardingException("融担号缺失，无法进行分库路由")
    }
    
    // 3. 应用分库路由
    hintManager.applyDatabaseHint(guaranteeNo)
}
```

**分表路由流程：**

```
routeToTable(QueryContext context, String tableName) {
    // 1. 提取分片键
    shardingKey = extractShardingKey(context)
    
    // 2. 应用分表路由
    if (shardingKey != null) {
        hintManager.applyTableHint(tableName, shardingKey)
        context.setHasShardingKey(true)
    } else {
        context.setHasShardingKey(false)  // 标记为广播查询
    }
}
```

**分片键提取策略：**

| 操作类型 | 提取策略 | 示例 |
|---------|----------|------|
| INSERT/UPDATE | 从实体对象的ID字段提取 | entity.getId() |
| SELECT | 从方法参数中查找String类型的ID | findById(String id) |
| SQL解析 | 正则匹配WHERE id = 'xxx' | WHERE id = 'ORDER_123' |

## 4.3 事务一致性保障机制

### 4.3.1 主表与索引表的原子性写入

为了确保主表和索引表的数据一致性，我们设计了统一的写操作处理机制：

+ **事务边界统一**：主表和索引表的操作在同一个事务中完成
+ **异常回滚**：任何一个操作失败都会触发整个事务回滚
+ **操作顺序**：先执行主表操作，成功后再更新索引表

**写操作处理流程：**

```
@Transactional
handle(QueryContext context, ProceedingJoinPoint point) {
    // 1. 应用路由策略
    dbRoutingHandler.routeToDatabase(context)
    tableRoutingHandler.routeToTable(context, tableName)
    
    // 2. 执行主表操作
    result = point.proceed()
    
    // 3. 同步更新索引表
    if (needsIndexTableUpdate(context)) {
        try {
            indexTableManager.updateIndexRecord(context, result)
        } catch (Exception e) {
            throw new IndexTableUpdateException(e)  // 触发事务回滚
        }
    }
    
    return result
}
```

### 4.3.2 索引表同步机制

**索引记录结构：**

```sql
CREATE TABLE t_order_index (
    id VARCHAR(64) PRIMARY KEY,     -- 订单ID（分片键）
    user_id VARCHAR(32),            -- 用户ID
    order_no VARCHAR(64),           -- 订单号
    phone VARCHAR(20),              -- 手机号
    create_time DATETIME            -- 创建时间
);
```

**索引同步策略：**

| 操作类型 | 索引表处理 | 说明 |
|---------|-----------|------|
| INSERT | 插入索引记录 | 提取关键字段插入索引表 |
| UPDATE | 更新索引记录 | 先尝试更新，失败则插入 |
| DELETE | 暂不处理 | 依赖数据生命周期管理 |

## 4.4 分片算法的具体实现

### 4.4.1 自定义Hint分片算法

我们实现了基于Hint的分片算法，支持从上下文获取路由信息，无需在SQL中显式包含分片条件。

**分库算法逻辑：**

```
GuaranteeNoHintShardingAlgorithm.doSharding() {
    // 1. 提取融担号
    guaranteeNo = extractGuaranteeNo(shardingValue)
    
    // 2. 计算目标数据库索引
    dbIndex = abs(guaranteeNo.hashCode()) % availableTargetNames.size()
    
    // 3. 查找目标数据库
    targetDb = findDatabaseByIndex(availableTargetNames, dbIndex)
    
    return [targetDb]
}
```

**分表算法逻辑：**

```
BusinessKeyHintShardingAlgorithm.doSharding() {
    // 1. 提取分片键
    shardingKey = extractShardingKey(shardingValue)
    
    // 2. 根据分片键决定路由策略
    if (shardingKey != null) {
        // 精确路由：计算目标表
        tableIndex = abs(shardingKey.hashCode()) % TABLE_COUNT
        targetTable = findTableByIndex(availableTargetNames, tableIndex)
        return [targetTable]
    } else {
        // 广播查询：返回所有表
        return availableTargetNames
    }
}
```

**算法配置参数：**

| 参数 | 值 | 说明 |
|------|----|----- |
| TABLE_COUNT | 128 | 单库内分表数量 |
| 哈希算法 | hashCode() % count | 简单取模算法 |
| 广播条件 | shardingKey == null | 无分片键时广播 |

## 4.5 查询策略执行引擎

### 4.5.1 查询策略的判断与分发

读操作处理器负责根据查询上下文选择合适的查询策略：

```
ReadOperationHandler.handle(context, point) {
    // 1. 应用分库路由
    dbRoutingHandler.routeToDatabase(context)
    
    // 2. 尝试应用分表路由
    tableRoutingHandler.routeToTable(context, tableName)
    
    // 3. 确定查询策略
    strategy = strategyManager.determineStrategy(context)
    
    // 4. 根据策略执行查询
    return executeQuery(strategy, context, point)
}
```

**策略选择逻辑：**

```
determineStrategy(QueryContext context) {
    if (context.hasShardingKey()) {
        return DIRECT_SHARDING
    }
    
    switch (context.getReturnType()) {
        case SINGLE_OBJECT:
            return INDEX_THEN_SCAN
        case LIST:
            return LIMITED_SCAN
        case PAGE:
            return context.isRandomPagination() ? FORBIDDEN : SCROLL_SCAN
        case AGGREGATION:
            return AGGREGATION_SCAN
        default:
            return FORBIDDEN
    }
}
```

### 4.5.2 索引表查询的两阶段执行

**索引表查询流程：**

```
IndexTableQueryExecutor.executeWithFallback(context, point) {
    try {
        // 第一阶段：查询索引表
        shardingKeys = queryIndexTable(context)
        
        if (shardingKeys.isEmpty()) {
            // 索引表未命中，降级为全表扫描
            return degradedQueryExecutor.execute(LIMITED_SCAN, context, point)
        }
        
        // 第二阶段：根据分片键精确查询
        return executeShardingQuery(shardingKeys, context, point)
        
    } catch (Exception e) {
        // 异常时降级处理
        return degradedQueryExecutor.execute(LIMITED_SCAN, context, point)
    }
}
```

### 4.5.3 降级查询的安全控制

**降级查询限制：**

```
DegradedQueryExecutor.execute(strategy, context, point) {
    // 1. 应用安全限制
    applySafetyLimits(strategy, context)
    
    // 2. 设置查询超时
    return executeWithTimeout(point, timeoutSeconds)
}

applySafetyLimits(strategy, context) {
    switch (strategy) {
        case LIMITED_SCAN:
            context.setForcedLimit(maxResults)  // 强制LIMIT
            logWarning("Limited scan query executed", context)
            break
        case SCROLL_SCAN:
            if (context.getPageSize() > maxResults) {
                throw QueryException("分页大小超过限制")
            }
            break
    }
}
```

**安全控制参数：**

| 参数 | 默认值 | 说明 |
|------|--------|------|
| maxScanTables | 32 | 最大扫描表数 |
| timeoutSeconds | 10 | 查询超时时间 |
| maxResults | 100 | 最大返回结果数 |

通过这套完整的核心实现架构，我们实现了：

1. **统一的AOP切面**：职责清晰的分层架构
2. **强制路由机制**：基于HintManager的精确路由
3. **事务一致性**：主表和索引表的原子性操作
4. **灵活的分片算法**：支持多种分片策略
5. **智能的查询策略**：根据场景自动选择最优执行方式
6. **完善的安全控制**：超时、限流、监控等保障机制

这套实现为分库分表系统提供了坚实的技术基础，确保了系统的高性能、高可用和可维护性。
