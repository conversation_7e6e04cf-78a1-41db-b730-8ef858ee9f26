# 3. 库内分表与热数据索引方案

## 3.1 分表的必要性

随着业务的持续高速增长，尽管现有的分库机制有效分散了整体数据压力，但新的性能瓶颈在**分库内部**浮现。部分融担数据高度集中，导致其所在的单个物理分库中，核心业务表（如 `t_trans_order`）的数据量再次达到性能极限，单表数据规模已达数亿级别。

这种库内单表的过度膨胀，已引发以下核心痛点：

+ **查询性能急剧下降：** 对大表的查询，尤其是非主键查询和分页查询，响应时间显著增长，严重影响用户体验。
+ **数据库维护困难：** 对大表的DDL操作（如加索引、改字段）耗时漫长，锁表风险高，数据库的日常维护和迭代变得异常困难。
+ **潜在的稳定性风险：** 大规模的慢查询持续消耗数据库资源，对整个分库的稳定性构成严重威胁。

**解决方案：** 在现有成熟、稳定的分库体系之上，引入**库内分表**（Table Sharding）机制。通过将单库内的大表进一步水平拆分为多张物理子表，将单点的数据压力和查询负载均匀分散。

## 3.2 分表策略设计

### 3.2.1 分片键选择

+ **分库键:** 融担号维持不变，以确保对现有分库逻辑的兼容性。
+ **库内分表键:** 
    - **优先使用：** 服务级统一分片键探索。在单个服务内部，寻找一个"黄金业务分表键"。这个分表键可以作为该服务内所有核心表的统一分片标准，这样所有的核心查询和修改都可以通过分表键精准路由物理表。然而，此类普适的"黄金分片键"通常难以找到。
    - **降级到单表：** 若确认不存在则降级为单表级别。鉴于存量业务的复杂性，基于不改业务的逻辑，将分表键的决策粒度被细化至每一张独立的数据表。对于每张待分表的表，以及对于业务的理解，需要考虑的问题是："对于这张表，用哪个字段做分片键，能让最多的核心查询语句最高效？"

通过此层决策，项目内绝大多数的表都将拥有一个最**适合自身业务场景的分片键**。

### 3.2.2 分表数量规划

+ **分表数量：** 综合考虑未来2-3年的业务增长、单表容量以及DDL广播效率，确定单库内分表数量为 **128** 张。

+ **单表数据量压力评估**
    - **5亿存量 +日增300万**
        * **两年后单库总数据量:** `300万/天 * 720天 ≈ 21亿`
        * **单表承载数据量:** `21亿 / 128张表 ≈` **1687.5万**
        * **结论:** 单表数据量低于2000万的理想值，此风险被认为是可接受的。
    - **10亿存量 + 日增 1000万**
        * **两年后单库总数据量:** `1000万/天 * 720天 ≈ 72亿`
        * **单表承载数据量:** `72亿 / 128张表 ≈` **5625万**
        * **结论:** 单表数据量高于2000万的理想值，不增加分表的前提下, 需要减少存储时长, 不过此类表较少, 目前支付模块仅trans_order表在8亿存量+900w增量左右, 其他系统未做调研。

### 3.2.3 分片算法实现

采用哈希取模算法确保数据均匀分布：

```java
/**
 * 哈希取模分片算法实现
 * 用于将数据根据分片键均匀分布到128张分表中
 */
public class HashModShardingAlgorithm implements StandardShardingAlgorithm<String> {

    /**
     * 分表数量：128张
     * 基于2-3年业务增长预估，单表数据量控制在2000万以内
     */
    private static final int SHARD_COUNT = 128;

    /**
     * 执行分片路由逻辑
     * @param availableTargetNames 可用的目标表名集合
     * @param shardingValue 分片键值对象
     * @return 目标分表名称
     */
    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           ShardingValue<String> shardingValue) {
        // 获取分片键的值（如订单ID、用户ID等）
        String value = shardingValue.getValue();

        // 计算哈希值，确保相同的分片键总是路由到同一张表
        int hashCode = value.hashCode();

        // 取绝对值并对分表数量取模，得到分表索引（0-127）
        int shardIndex = Math.abs(hashCode) % SHARD_COUNT;

        // 根据索引找到对应的物理表名
        return findTargetByIndex(availableTargetNames, shardIndex);
    }

    /**
     * 根据分表索引查找对应的物理表名
     * @param targets 所有可用的表名集合
     * @param index 分表索引（0-127）
     * @return 匹配的表名，格式如：t_order_0, t_order_1, ..., t_order_127
     */
    private String findTargetByIndex(Collection<String> targets, int index) {
        return targets.stream()
                // 过滤出以"_索引号"结尾的表名
                .filter(target -> target.endsWith("_" + index))
                .findFirst()
                // 如果找不到对应的表，抛出异常（配置错误）
                .orElseThrow(() -> new IllegalStateException("No target found for index: " + index));
    }
}
```

## 3.3 非分表键查询的性能挑战

分表后，所有不带分片键的查询将无法路由到具体的物理表，面临以下问题：

+ **全表扫描风险：** ShardingSphere在无分片键时，默认会对所有分片执行广播查询，在128个分片上并行执行，瞬间对数据库造成巨大I/O和连接池压力。
+ **性能急剧下降：** 原本的单表查询变成了128个表的并行查询，响应时间和资源消耗成倍增长。
+ **系统稳定性威胁：** 大量的广播查询可能引发数据库雪崩，严重威胁系统稳定性。

**传统解决方案的局限性：**
- 外部搜索引擎（如ES）：增加系统复杂度，存在数据一致性问题
- 冗余字段：业务侵入性强，维护成本高
- 中间件路由表：需要额外的存储和维护成本

## 3.4 热数据索引表解决方案

### 3.4.1 热数据模型

基于业务访问模式的分析，我们发现一个重要规律：**最近产生的数据具有最高的访问频率**。例如：
- 90%以上的订单查询集中在最近2周的数据
- 用户主要关注最新的交易记录和状态
- 历史数据的访问频率随时间呈指数级下降

基于这个**热数据模型**，我们设计了一个轻量级的索引表方案。

### 3.4.2 索引表设计

我们创建一张普通的单表 `t_order_index`，专门存储**非分片键到分片键的映射关系**：

```sql
CREATE TABLE `t_order_index` (
  `id` VARCHAR(64) NOT NULL COMMENT '订单ID，与主表ID一致',
  `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号', 
  `phone` VARCHAR(20) COMMENT '手机号',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_order_no` (`order_no`),
  INDEX `idx_phone` (`phone`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='订单热数据索引表';
```

**设计要点：**
- **普通单表结构**：无分区，结构简单，维护方便
- **映射关系存储**：存储业务查询中常用的非分片键字段
- **主键就是分片键**：通过ID可以直接路由到具体分表
- **精简字段**：只存储查询必需的字段，保持表结构轻量

### 3.4.3 查询流程

所有非分片键查询都通过两阶段查询完成：

1. **第一阶段：索引表查询**
   ```sql
   -- 根据用户ID查询获取订单ID列表
   SELECT id FROM t_order_index WHERE user_id = 'user123';
   ```

2. **第二阶段：精确分表查询**
   ```sql
   -- 根据ID精确路由到具体分表查询完整数据
   SELECT * FROM t_order WHERE id IN ('order1', 'order2', 'order3');
   ```

**性能优势：**
- 索引表查询：基于索引的高效查询，毫秒级响应
- 分表查询：通过分片键精确路由，避免广播查询
- 整体性能：两次精确查询的总耗时远低于128个分片的广播查询

## 3.5 索引表的生命周期管理

### 3.5.1 数据保留策略与热度统计

+ **动态配置的时间窗口：** 索引表只保留最近一段时间的热数据，通过配置中心动态设置保留时间。
+ **热度统计机制：**
  - 可选开启的查询热度统计功能
  - 记录不同时间范围内的查询频率分布
  - 为时间窗口优化提供数据支撑
  - 通过配置控制是否启用，避免对性能产生影响

+ **配置化管理：** 通过配置中心统一管理数据保留时间，支持动态调整。

```properties
# 索引表数据保留配置
index.table.retention.days=14
index.table.cleanup.schedule=0 2 * * *
index.table.stats.enabled=true
index.table.stats.sample.rate=0.01
```

**热度统计实现：**

```java
@Component
public class QueryHeatStatisticsCollector {

    @Value("${index.table.stats.enabled:false}")
    private boolean statsEnabled;

    @Value("${index.table.stats.sample.rate:0.01}")
    private double sampleRate;

    public void recordQuery(Date queryTime) {
        if (!statsEnabled || !shouldSample()) {
            return;
        }

        // 记录查询时间分布
        int daysAgo = calculateDaysFromNow(queryTime);
        incrementQueryCount(daysAgo);
    }

    private boolean shouldSample() {
        return Math.random() < sampleRate;
    }

    // 定期分析并推荐最优时间窗口
    @Scheduled(cron = "0 0 1 * * ?")
    public void analyzeAndRecommend() {
        if (!statsEnabled) {
            return;
        }

        Map<Integer, Long> distribution = getQueryDistribution();
        int recommendedDays = calculateOptimalRetentionDays(distribution);
        log.info("Recommended retention days: {}", recommendedDays);
    }
}
```

### 3.5.2 定时清理脚本

设计自动化的数据清理机制：

```sql
-- 清理脚本示例
DELETE FROM t_order_index
WHERE create_time < DATE_SUB(NOW(), INTERVAL ${retention_days} DAY)
LIMIT 10000;
```

**清理策略：**
- **分批删除：** 每次删除固定数量的记录，避免长时间锁表
- **定时执行：** 在业务低峰期执行，减少对线上业务的影响
- **监控告警：** 监控清理脚本的执行状态，确保数据生命周期管理正常

### 3.5.3 查询场景与降级策略

#### 热数据索引表的固有局限性

热数据索引表虽然有效解决了分表后的查询路由问题，但其设计也带来了一些固有的局限性：

+ **时间窗口限制**：只能查询时间窗口内的热数据，超出范围的历史数据无法通过索引表快速定位
+ **查询模式受限**：为了保持索引表的简洁性和高性能，只能支持有限的查询模式
+ **存储成本**：需要额外的存储空间来维护索引映射关系

#### 按增删改查划分的支持场景

基于业务操作类型和返回值特征，我们对查询场景进行了精确的分级支持定义：

**增（INSERT）操作**
- ✅ **完全支持**：同时更新分表和索引表，保证数据一致性

**改（UPDATE）操作**
- ✅ **完全支持**：同时更新分表和索引表，保证数据一致性

**查（SELECT）操作 - 分级支持（重点关注不带分表键的场景）**

*注：带分表键的查询是基础功能，必须完全支持，此处重点说明不带分表键的查询处理策略*

#### 不带分表键查询的分级处理

**第一级：单个对象返回（单点查询）**
- **场景**：`WHERE user_id = 'U123'` 返回单个Order对象
- **处理策略**：
  1. 先查询索引表获取分表键
  2. 若命中，直接查询对应分表
  3. 若未命中，执行有限制的全表扫描
- **性能特点**：大部分情况下性能优异

**第二级：列表返回（多条数据）**
- **场景**：`WHERE user_id = 'U123'` 返回List<Order>
- **处理策略**：
  - 强制增加LIMIT限制（默认20条，可配置）
  - 打印警告日志记录此类查询
  - 执行全表扫描获取结果
- **限制说明**：防止大批量数据查询影响系统性能

**第三级：聚合查询**
- **场景**：`SELECT COUNT(*) WHERE user_id = 'U123'`
- **处理策略**：支持基础聚合操作（COUNT、SUM、AVG等）
- **性能特点**：聚合查询性能相对较高，可以接受

**第四级：受限的分页查询**
- **滚动分页**：
  - ✅ **有限支持**：分页大小最多20条，超过报错
  - 不走索引表，直接全表扫描
  - 适用于数据浏览场景
- **随机分页**：
  - ❌ **不支持**：基于OFFSET的分页会导致性能灾难

**完全不支持的查询类型**
- ❌ **排序查询**：`WHERE user_id = 'U123' ORDER BY create_time`（无分表键的排序）
- ❌ **复合条件查询**：`WHERE user_id = 'U123' AND phone = '138xxx'`（多个非分表键字段）
- ❌ **多表查询**：涉及JOIN操作的查询
- ❌ **子查询**：包含嵌套查询的复杂SQL
- ❌ **范围查询**：`WHERE create_time BETWEEN '2024-01-01' AND '2024-01-31'`
- ❌ **模糊查询**：`WHERE order_no LIKE 'O2024%'`

**历史数据查询（时间窗口外）**
- **处理方式**：提示用户数据超出查询范围
- **替代方案**：引导用户使用数据仓库或离线查询服务

#### 分级查询策略的技术实现

```java
@Component
public class QueryStrategyManager {

    @Value("${query.list.max.limit:20}")
    private int maxListLimit;

    @Value("${query.pagination.max.size:20}")
    private int maxPaginationSize;

    @Value("${query.scan.timeout.seconds:10}")
    private int scanTimeoutSeconds;

    public QueryStrategy determineStrategy(QueryContext context) {
        // 1. 检查是否包含分表键
        if (context.hasShardingKey()) {
            return QueryStrategy.DIRECT_SHARDING; // 直接查询分表
        }

        // 2. 根据返回值类型确定策略
        switch (context.getReturnType()) {
            case SINGLE_OBJECT:
                return handleSingleObjectQuery(context);
            case LIST:
                return handleListQuery(context);
            case PAGE:
                return handlePageQuery(context);
            case AGGREGATION:
                return handleAggregationQuery(context);
            default:
                return QueryStrategy.FORBIDDEN;
        }
    }

    private QueryStrategy handleSingleObjectQuery(QueryContext context) {
        if (isForbiddenQueryType(context)) {
            return QueryStrategy.FORBIDDEN;
        }

        // 单点查询：先查索引表，未命中则全表扫描
        return QueryStrategy.INDEX_THEN_SCAN;
    }

    private QueryStrategy handleListQuery(QueryContext context) {
        if (isForbiddenQueryType(context)) {
            return QueryStrategy.FORBIDDEN;
        }

        // 列表查询：强制LIMIT，全表扫描
        context.setForcedLimit(maxListLimit);
        logWarning("List query without sharding key", context);
        return QueryStrategy.LIMITED_SCAN;
    }

    private QueryStrategy handlePageQuery(QueryContext context) {
        if (context.isRandomPagination()) {
            return QueryStrategy.FORBIDDEN; // 不支持随机分页
        }

        if (context.getPageSize() > maxPaginationSize) {
            throw new QueryException("分页大小超过限制: " + maxPaginationSize);
        }

        // 滚动分页：全表扫描
        return QueryStrategy.SCROLL_SCAN;
    }

    private QueryStrategy handleAggregationQuery(QueryContext context) {
        if (isForbiddenQueryType(context)) {
            return QueryStrategy.FORBIDDEN;
        }

        // 聚合查询：支持
        return QueryStrategy.AGGREGATION_SCAN;
    }

    private boolean isForbiddenQueryType(QueryContext context) {
        return context.hasOrderBy() ||           // 排序查询（无分表键）
               context.hasMultipleConditions() || // 复合条件
               context.hasRangeCondition() ||     // 范围查询
               context.hasLikeCondition() ||      // 模糊查询
               context.hasJoin() ||               // 多表查询
               context.hasSubQuery();             // 子查询
    }

    private void logWarning(String message, QueryContext context) {
        log.warn("{}: SQL={}, Method={}", message, context.getSql(), context.getMethodName());
        // 记录到监控系统
        queryMonitor.recordWarning(message, context);
    }
}

enum QueryStrategy {
    DIRECT_SHARDING,    // 直接查询分表（带分表键）
    INDEX_THEN_SCAN,    // 先查索引表，未命中则扫描（单点查询）
    LIMITED_SCAN,       // 有限制的全表扫描（列表查询）
    SCROLL_SCAN,        // 滚动分页扫描
    AGGREGATION_SCAN,   // 聚合查询扫描
    FORBIDDEN           // 禁止查询
}

enum ReturnType {
    SINGLE_OBJECT,      // 单个对象
    LIST,              // 列表
    PAGE,              // 分页对象
    AGGREGATION        // 聚合结果
}

public class QueryContext {
    private String sql;
    private String methodName;
    private ReturnType returnType;
    private boolean hasShardingKey;
    private int pageSize;
    private boolean isRandomPagination;
    private int forcedLimit;

    // 查询特征检查方法
    public boolean hasOrderBy() { /* 检查是否有ORDER BY */ }
    public boolean hasMultipleConditions() { /* 检查是否有多个WHERE条件 */ }
    public boolean hasRangeCondition() { /* 检查是否有范围查询 */ }
    public boolean hasLikeCondition() { /* 检查是否有LIKE查询 */ }
    public boolean hasJoin() { /* 检查是否有JOIN */ }
    public boolean hasSubQuery() { /* 检查是否有子查询 */ }

    // getter/setter方法...
}
```

**查询监控统计：**

```java
@Component
public class QueryMonitor {

    private final Counter directShardingCounter;
    private final Counter indexThenScanCounter;
    private final Counter limitedScanCounter;
    private final Counter forbiddenQueryCounter;

    @PostConstruct
    public void init() {
        directShardingCounter = meterRegistry.counter("query.direct.sharding");
        indexThenScanCounter = meterRegistry.counter("query.index.then.scan");
        limitedScanCounter = meterRegistry.counter("query.limited.scan");
        forbiddenQueryCounter = meterRegistry.counter("query.forbidden");
    }

    public void recordQueryStrategy(QueryStrategy strategy) {
        switch (strategy) {
            case DIRECT_SHARDING:
                directShardingCounter.increment();
                break;
            case INDEX_THEN_SCAN:
                indexThenScanCounter.increment();
                break;
            case LIMITED_SCAN:
            case SCROLL_SCAN:
            case AGGREGATION_SCAN:
                limitedScanCounter.increment();
                break;
            case FORBIDDEN:
                forbiddenQueryCounter.increment();
                break;
        }
    }

    public void recordWarning(String message, QueryContext context) {
        log.warn("Query Warning: {} - SQL: {}", message, context.getSql());
        // 发送到监控系统
        alertManager.sendWarning(message, context);
    }
}
```
```

**降级查询监控：**

```java
@Component
public class DegradedQueryMonitor {

    private Counter degradedQueryCounter;
    private Timer degradedQueryTimer;

    @PostConstruct
    public void init() {
        degradedQueryCounter = meterRegistry.counter("query.degraded.count");
        degradedQueryTimer = meterRegistry.timer("query.degraded.time");
    }

    public void recordDegradedQuery(String queryType, long executionTime) {
        degradedQueryCounter.increment();

        // 记录执行时间
        degradedQueryTimer.record(executionTime, TimeUnit.MILLISECONDS);

        // 超过阈值告警
        if (executionTime > alertThresholdMs) {
            alertSlowDegradedQuery(queryType, executionTime);
        }
    }
}
```

### 3.5.4 不支持场景的替代方案

对于索引表不适合支持的查询场景，我们提供以下替代方案：

1. **数据仓库查询**：复杂的历史数据分析和报表查询应通过数据仓库系统实现
2. **离线批处理**：大范围的数据导出和统计应使用离线批处理作业
3. **专门的查询服务**：为运营和客服等特殊场景提供独立的查询服务，可以有更宽松的资源限制

通过这套热数据索引表方案，我们在保持系统简洁性的同时，有效解决了分表后非分片键查询的性能问题，为绝大多数业务场景提供了高效的查询能力，同时为特殊场景提供了明确的替代方案。
